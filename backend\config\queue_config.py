import os
from typing import Optional
from pydantic import BaseSettings, Field


class QueueConfig(BaseSettings):
    """Configuration for Upstash QStash message queue."""
    
    # QStash Configuration
    qstash_token: str = Field(..., env="QSTASH_TOKEN")
    qstash_url: str = Field(default="https://qstash.upstash.io", env="QSTASH_URL")
    
    # Queue Names
    render_queue_name: str = Field(default="render-jobs", env="RENDER_QUEUE_NAME")
    batch_render_queue_name: str = Field(default="batch-render-jobs", env="BATCH_RENDER_QUEUE_NAME")
    
    # Queue Settings
    queue_parallelism: int = Field(default=1, env="QUEUE_PARALLELISM")
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    
    # Worker Configuration
    worker_base_url: str = Field(..., env="WORKER_BASE_URL")
    worker_secret: str = Field(..., env="WORKER_SECRET")
    
    # Job Settings
    job_timeout_seconds: int = Field(default=300, env="JOB_TIMEOUT_SECONDS")  # 5 minutes
    cleanup_completed_jobs_after_hours: int = Field(default=24, env="CLEANUP_COMPLETED_JOBS_AFTER_HOURS")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global configuration instance
queue_config = QueueConfig()


def get_queue_config() -> QueueConfig:
    """Get the global queue configuration instance."""
    return queue_config


def validate_config() -> bool:
    """Validate that all required configuration is present."""
    try:
        config = get_queue_config()
        required_fields = [
            config.qstash_token,
            config.worker_base_url,
            config.worker_secret
        ]
        return all(field for field in required_fields)
    except Exception:
        return False
