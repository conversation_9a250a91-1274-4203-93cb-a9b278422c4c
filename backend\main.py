import os
import logging
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from typing import Optional, List

# Import queue-related modules
from models.job_models import (
    CreateRenderJobRequest,
    CreateBatchRenderJobRequest,
    JobResponse,
    JobStatusResponse,
    RenderJobData,
    JobStatus,
)
from services.queue_manager import get_queue_manager
from workers.worker_endpoints import worker_router
from config.queue_config import validate_config, get_queue_config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ─── App Initialization ────────────────────────────────────────────────────────
app = FastAPI(
    title="ClarifAI Render API", description="Queue-based video rendering API"
)
os.makedirs("media", exist_ok=True)

# Validate configuration on startup
if not validate_config():
    logger.error("Invalid configuration. Please check your environment variables.")
    raise RuntimeError("Configuration validation failed")

# Include worker endpoints
app.include_router(worker_router)

# Mount static files for serving rendered videos
app.mount("/media", StaticFiles(directory="media"), name="media")


# ─── Legacy Data Models (for backward compatibility) ───────────────────────────
class RenderRequest(BaseModel):
    script: str
    scene_name: Optional[str] = None


class BatchRenderRequest(BaseModel):
    scripts: List[RenderRequest]


# ─── Routes ────────────────────────────────────────────────────────────────────
@app.get("/")
async def root():
    return {
        "message": "Welcome to the ClarifAI Render API with Queue System. Use POST /render or /batch_render to queue rendering jobs."
    }


@app.post("/render", response_model=JobResponse)
async def queue_render_job(req: CreateRenderJobRequest) -> JobResponse:
    """Queue a render job for processing."""
    try:
        queue_manager = get_queue_manager()

        job_id = queue_manager.queue_render_job(
            script=req.script, scene_name=req.scene_name, priority=req.priority
        )

        logger.info(f"Render job {job_id} queued successfully")

        return JobResponse(
            job_id=job_id,
            status=JobStatus.QUEUED,
            message="Job queued successfully. Use /jobs/{job_id} to check status.",
        )

    except Exception as e:
        logger.error(f"Failed to queue render job: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to queue job: {str(e)}")


@app.post("/batch_render", response_model=JobResponse)
async def queue_batch_render_job(req: CreateBatchRenderJobRequest) -> JobResponse:
    """Queue a batch render job for processing."""
    try:
        queue_manager = get_queue_manager()

        # Convert to RenderJobData format
        scripts = [
            RenderJobData(script=script.script, scene_name=script.scene_name)
            for script in req.scripts
        ]

        job_id = queue_manager.queue_batch_render_job(
            scripts=scripts, priority=req.priority
        )

        logger.info(f"Batch render job {job_id} queued successfully")

        return JobResponse(
            job_id=job_id,
            status=JobStatus.QUEUED,
            message="Batch job queued successfully. Use /jobs/{job_id} to check status.",
        )

    except Exception as e:
        logger.error(f"Failed to queue batch render job: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to queue job: {str(e)}")


# ─── Job Status and Monitoring Endpoints ──────────────────────────────────────
@app.get("/jobs/{job_id}", response_model=JobStatusResponse)
async def get_job_status(job_id: str) -> JobStatusResponse:
    """Get the status of a specific job."""
    try:
        queue_manager = get_queue_manager()
        job_status = queue_manager.get_job_status(job_id)

        if not job_status:
            raise HTTPException(status_code=404, detail="Job not found")

        # Calculate progress percentage
        progress_percentage = None
        if job_status.status == JobStatus.PROCESSING:
            progress_percentage = 50.0  # Simple progress indication
        elif job_status.status == JobStatus.COMPLETED:
            progress_percentage = 100.0

        return JobStatusResponse(
            job_id=job_status.job_id,
            job_type=job_status.job_type,
            status=job_status.status,
            created_at=job_status.created_at,
            started_at=job_status.started_at,
            completed_at=job_status.completed_at,
            progress_percentage=progress_percentage,
            result=job_status.result,
            error_message=job_status.last_error,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get job status: {e}")
        raise HTTPException(status_code=500, detail="Failed to get job status")


@app.get("/jobs")
async def list_jobs(
    status: Optional[str] = None, job_type: Optional[str] = None, limit: int = 100
):
    """List jobs with optional filtering."""
    try:
        queue_manager = get_queue_manager()

        # Parse filters
        status_filter = None
        if status:
            try:
                status_filter = JobStatus(status)
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid status: {status}")

        job_type_filter = None
        if job_type:
            from models.job_models import JobType

            try:
                job_type_filter = JobType(job_type)
            except ValueError:
                raise HTTPException(
                    status_code=400, detail=f"Invalid job type: {job_type}"
                )

        jobs = queue_manager.list_jobs(
            status_filter=status_filter,
            job_type_filter=job_type_filter,
            limit=min(limit, 1000),  # Cap at 1000
        )

        return {
            "jobs": [
                {
                    "job_id": job.job_id,
                    "job_type": job.job_type,
                    "status": job.status,
                    "created_at": job.created_at,
                    "started_at": job.started_at,
                    "completed_at": job.completed_at,
                    "queue_name": job.queue_name,
                }
                for job in jobs
            ],
            "total": len(jobs),
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to list jobs: {e}")
        raise HTTPException(status_code=500, detail="Failed to list jobs")


@app.delete("/jobs/{job_id}")
async def cancel_job(job_id: str):
    """Cancel a job if it's not already processing or completed."""
    try:
        queue_manager = get_queue_manager()

        if queue_manager.cancel_job(job_id):
            return {"message": f"Job {job_id} cancelled successfully"}
        else:
            job_status = queue_manager.get_job_status(job_id)
            if not job_status:
                raise HTTPException(status_code=404, detail="Job not found")
            else:
                raise HTTPException(
                    status_code=400,
                    detail=f"Cannot cancel job in status: {job_status.status}",
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel job: {e}")
        raise HTTPException(status_code=500, detail="Failed to cancel job")


@app.get("/queues/stats")
async def get_queue_stats():
    """Get statistics for all queues."""
    try:
        queue_manager = get_queue_manager()
        config = get_queue_config()

        render_stats = queue_manager.get_queue_stats(config.render_queue_name)
        batch_render_stats = queue_manager.get_queue_stats(
            config.batch_render_queue_name
        )

        return {
            "queues": {
                "render": render_stats.model_dump(),
                "batch_render": batch_render_stats.model_dump(),
            },
            "total_active_jobs": render_stats.get_active_jobs()
            + batch_render_stats.get_active_jobs(),
        }

    except Exception as e:
        logger.error(f"Failed to get queue stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get queue statistics")


# ─── Error Handling and Retry Endpoints ───────────────────────────────────────
@app.post("/jobs/{job_id}/retry")
async def retry_job(job_id: str):
    """Retry a failed job if it can be retried."""
    try:
        queue_manager = get_queue_manager()

        if queue_manager.retry_failed_job(job_id):
            return {"message": f"Job {job_id} queued for retry"}
        else:
            job_status = queue_manager.get_job_status(job_id)
            if not job_status:
                raise HTTPException(status_code=404, detail="Job not found")
            else:
                raise HTTPException(
                    status_code=400,
                    detail=f"Cannot retry job in status: {job_status.status} (retries: {job_status.current_retry}/{job_status.max_retries})",
                )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to retry job: {e}")
        raise HTTPException(status_code=500, detail="Failed to retry job")


@app.get("/jobs/failed")
async def get_failed_jobs():
    """Get all failed jobs that can potentially be retried."""
    try:
        queue_manager = get_queue_manager()
        failed_jobs = queue_manager.get_failed_jobs()

        return {
            "failed_jobs": [
                {
                    "job_id": job.job_id,
                    "job_type": job.job_type,
                    "created_at": job.created_at,
                    "current_retry": job.current_retry,
                    "max_retries": job.max_retries,
                    "last_error": job.last_error,
                    "can_retry": job.can_retry(),
                }
                for job in failed_jobs
            ],
            "total": len(failed_jobs),
        }

    except Exception as e:
        logger.error(f"Failed to get failed jobs: {e}")
        raise HTTPException(status_code=500, detail="Failed to get failed jobs")


@app.post("/admin/cleanup")
async def cleanup_completed_jobs(older_than_hours: int = 24):
    """Clean up completed jobs older than specified hours."""
    try:
        queue_manager = get_queue_manager()
        cleaned_count = queue_manager.cleanup_completed_jobs(older_than_hours)

        return {
            "message": f"Cleaned up {cleaned_count} completed jobs older than {older_than_hours} hours"
        }

    except Exception as e:
        logger.error(f"Failed to cleanup jobs: {e}")
        raise HTTPException(status_code=500, detail="Failed to cleanup jobs")
