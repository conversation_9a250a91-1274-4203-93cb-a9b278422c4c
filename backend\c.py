import httpx
import time
from concurrent.futures import ThreadPoolExecutor

BASE_URL = "http://localhost:8000"

sample_script = """
from manim import *

class HelloScene(Scene):
    def construct(self):
        text = Text("Hello from Manim!", font_size=64)
        self.play(Write(text))
        self.wait(1)
"""

def submit_and_track(idx):
    try:
        print(f"[{idx}] ▶️ Submitting job...")
        res = httpx.post(f"{BASE_URL}/render", json={"script": sample_script}, timeout=60)
        res.raise_for_status()
        data = res.json()
        job_id = data["job_id"]
        print(f"[{idx}] ✅ Submitted. Job ID: {job_id}")

        while True:
            status = httpx.get(f"{BASE_URL}/status/{job_id}", timeout=10).json()
            if status["status"] == "done":
                print(f"[{idx}] 🎉 Done: {status['result']['output_url']}")
                break
            elif status["status"] == "error":
                print(f"[{idx}] ❌ Error: {status['error']}")
                break
            time.sleep(2)
    except Exception as e:
        print(f"[{idx}] ❌ Failed: {e}")

def run_multiple_jobs(count: int = 5):
    with ThreadPoolExecutor(max_workers=count) as executor:
        for i in range(count):
            executor.submit(submit_and_track, i)

if __name__ == "__main__":
    run_multiple_jobs(5)  # change number to queue more jobs
