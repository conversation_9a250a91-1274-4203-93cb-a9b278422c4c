import logging
from fastapi import <PERSON><PERSON><PERSON><PERSON>, HTTPException, Request, Head<PERSON>
from typing import Optional

from models.job_models import JobRequest
from workers.job_processor import get_job_processor
from services.queue_manager import get_queue_manager
from config.queue_config import get_queue_config

logger = logging.getLogger(__name__)

# Create router for worker endpoints
worker_router = APIRouter(prefix="/worker", tags=["worker"])


@worker_router.post("/render")
async def process_render_job(
    request: Request,
    x_worker_secret: Optional[str] = Header(None, alias="X-Worker-Secret"),
    upstash_signature: Optional[str] = Header(None, alias="Upstash-Signature"),
):
    """Process a render job from the queue."""
    config = get_queue_config()
    queue_manager = get_queue_manager()
    job_processor = get_job_processor()

    # Verify worker secret
    if x_worker_secret != config.worker_secret:
        logger.warning("Invalid worker secret in render job request")
        raise HTTPException(status_code=401, detail="Invalid worker secret")

    try:
        # Get request body
        body = await request.body()
        body_str = body.decode("utf-8")

        # Verify QStash signature if provided
        if upstash_signature:
            if not queue_manager.verify_webhook_signature(
                body=body_str, signature=upstash_signature, url=str(request.url)
            ):
                logger.warning("Invalid QStash signature in render job request")
                raise HTTPException(status_code=401, detail="Invalid signature")

        # Parse job request
        import json
        from datetime import datetime

        job_data = json.loads(body_str)

        # Handle datetime parsing if it's a string
        if "created_at" in job_data and isinstance(job_data["created_at"], str):
            job_data["created_at"] = datetime.fromisoformat(
                job_data["created_at"].replace("Z", "+00:00")
            )

        job_request = JobRequest(**job_data)

        logger.info(f"Processing render job: {job_request.job_id}")

        # Process the job
        result = job_processor.process_job(job_request)

        return {
            "status": "success",
            "job_id": job_request.job_id,
            "result": result.model_dump(),
        }

    except Exception as e:
        logger.error(f"Failed to process render job: {e}")
        raise HTTPException(status_code=500, detail=f"Job processing failed: {str(e)}")


@worker_router.post("/batch_render")
async def process_batch_render_job(
    request: Request,
    x_worker_secret: Optional[str] = Header(None, alias="X-Worker-Secret"),
    upstash_signature: Optional[str] = Header(None, alias="Upstash-Signature"),
):
    """Process a batch render job from the queue."""
    config = get_queue_config()
    queue_manager = get_queue_manager()
    job_processor = get_job_processor()

    # Verify worker secret
    if x_worker_secret != config.worker_secret:
        logger.warning("Invalid worker secret in batch render job request")
        raise HTTPException(status_code=401, detail="Invalid worker secret")

    try:
        # Get request body
        body = await request.body()
        body_str = body.decode("utf-8")

        # Verify QStash signature if provided
        if upstash_signature:
            if not queue_manager.verify_webhook_signature(
                body=body_str, signature=upstash_signature, url=str(request.url)
            ):
                logger.warning("Invalid QStash signature in batch render job request")
                raise HTTPException(status_code=401, detail="Invalid signature")

        # Parse job request
        import json
        from datetime import datetime

        job_data = json.loads(body_str)

        # Handle datetime parsing if it's a string
        if "created_at" in job_data and isinstance(job_data["created_at"], str):
            job_data["created_at"] = datetime.fromisoformat(
                job_data["created_at"].replace("Z", "+00:00")
            )

        job_request = JobRequest(**job_data)

        logger.info(f"Processing batch render job: {job_request.job_id}")

        # Process the job
        result = job_processor.process_job(job_request)

        return {
            "status": "success",
            "job_id": job_request.job_id,
            "result": result.model_dump(),
        }

    except Exception as e:
        logger.error(f"Failed to process batch render job: {e}")
        raise HTTPException(status_code=500, detail=f"Job processing failed: {str(e)}")


@worker_router.get("/health")
async def worker_health():
    """Health check endpoint for workers."""
    return {"status": "healthy", "message": "Worker is ready to process jobs"}
