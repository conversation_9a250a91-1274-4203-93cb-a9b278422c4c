import httpx
import time

BASE_URL = "http://localhost:8000"

sample_script = """
from manim import *

class HelloScene(Scene):
    def construct(self):
        text = Text("Hello from Manim!", font_size=64)
        self.play(Write(text))
        self.wait(1)
"""

def test_render():
    print("▶️ Submitting render job...")
    response = httpx.post(
        f"{BASE_URL}/render",
        json={"script": sample_script},
        timeout=60.0  # ⏱️ Increased timeout
    )
    response.raise_for_status()
    data = response.json()
    print("✅ Job Submitted:", data)
    job_id = data["job_id"]

    print("⏳ Waiting for result...")
    while True:
        status = httpx.get(f"{BASE_URL}/status/{job_id}", timeout=10.0).json()
        if status["status"] == "done":
            print("🎉 Render Complete:", status["result"]["output_url"])
            break
        elif status["status"] == "error":
            print("❌ Render Failed:", status["error"])
            break
        time.sleep(2)

if __name__ == "__main__":
    test_render()
