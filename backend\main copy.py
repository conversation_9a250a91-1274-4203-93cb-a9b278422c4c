import os, uuid, subprocess, shutil
from fastapi import FastAP<PERSON>, HTTPException, Request
from pydantic import Base<PERSON>ode<PERSON>
from typing import Optional, List
from enum import Enum
from threading import Lock
from qstash import QStash, Receiver
from dotenv import load_dotenv

# ─── Load Environment Variables ────────────────────────────────────────────────
load_dotenv()

# ─── Setup ──────────────────────────────────────────────────────────────────────
app = FastAPI()
os.makedirs("media", exist_ok=True)

QSTASH_TOKEN = os.getenv("QSTASH_TOKEN")
if not QSTASH_TOKEN:
    raise RuntimeError("Missing QSTASH_TOKEN env var")

qstash = QStash(QSTASH_TOKEN)
qstash.queue.upsert("render-queue", parallelism=1)

QSTASH_CURRENT_SIGNING_KEY = os.getenv("QSTASH_CURRENT_SIGNING_KEY")
if not QSTASH_CURRENT_SIGNING_KEY:
    raise RuntimeError("Missing QSTASH_CURRENT_SIGNING_KEY env var")
QSTASH_NEXT_SIGNING_KEY = os.getenv("QSTASH_NEXT_SIGNING_KEY")
if not QSTASH_NEXT_SIGNING_KEY:
    raise RuntimeError("Missing QSTASH_NEXT_SIGNING_KEY env var")

receiver = Receiver(
    current_signing_key=QSTASH_CURRENT_SIGNING_KEY,
    next_signing_key=QSTASH_NEXT_SIGNING_KEY,
)


# ─── Job Tracking ──────────────────────────────────────────────────────────────
class JobState(str, Enum):
    QUEUED = "queued"
    PROCESSING = "processing"
    DONE = "done"
    ERROR = "error"


job_statuses = {}
status_lock = Lock()


# ─── Models ─────────────────────────────────────────────────────────────────────
class RenderRequest(BaseModel):
    script: str
    scene_name: Optional[str] = None
    job_id: Optional[str] = None


class BatchRenderRequest(BaseModel):
    scripts: List[RenderRequest]
    job_id: Optional[str] = None


class QueueMessage(BaseModel):
    type: str
    payload: dict
    job_id: str


# ─── Utilities ─────────────────────────────────────────────────────────────────
def get_scene_name(script: str) -> str:
    lines = script.splitlines()
    for line in lines:
        if line.strip().startswith("class ") and "Scene" in line:
            return line.split("(")[0].replace("class ", "").strip()
    return "GenScene"


def _clear_tmp(tmp_dir: str):
    if os.path.exists(tmp_dir):
        shutil.rmtree(tmp_dir, ignore_errors=True)


# ─── Processing ────────────────────────────────────────────────────────────────
def process_render_logic(req: RenderRequest, job_id: str):
    tmp = os.path.abspath("tmp")
    _clear_tmp(tmp)
    os.makedirs(tmp, exist_ok=True)

    script_filename = f"script_{job_id}.py"
    script_path = os.path.join(tmp, script_filename)
    with open(script_path, "w", encoding="utf-8") as f:
        f.write(req.script)

    scene = req.scene_name or get_scene_name(req.script)
    cmd = f"manim -qh {script_filename} {scene}"
    subprocess.run(cmd, shell=True, cwd=tmp, capture_output=True, text=True, check=True)

    media_root = os.path.join(tmp, "media")
    out_path = None
    for root, _, files in os.walk(media_root):
        if script_filename.replace(".py", "") in root:
            for f in files:
                if f.endswith(f"{scene}.mp4") and "partial" not in root:
                    out_path = os.path.join(root, f)
                    break
    if not out_path:
        raise FileNotFoundError("Rendered video not found")

    final = os.path.join("media", job_id)
    os.makedirs(final, exist_ok=True)
    dest = os.path.join(final, f"{scene}.mp4")
    shutil.move(out_path, dest)
    _clear_tmp(tmp)
    return {
        "job_id": job_id,
        "scene_name": scene,
        "output_url": f"/media/{job_id}/{scene}.mp4",
    }


def process_batch_logic(batch: BatchRenderRequest, job_id: str):
    tmp = os.path.abspath("tmp")
    _clear_tmp(tmp)
    os.makedirs(tmp, exist_ok=True)

    paths = []
    for idx, req in enumerate(batch.scripts):
        single = process_render_logic(req, f"{job_id}_{idx}")
        paths.append(
            os.path.join("media", single["job_id"], f"{single['scene_name']}.mp4")
        )

    list_txt = os.path.join(tmp, "list.txt")
    with open(list_txt, "w", encoding="utf-8") as f:
        for p in paths:
            f.write(f"file '{os.path.abspath(p)}'\n")

    final = os.path.join("media", job_id)
    os.makedirs(final, exist_ok=True)
    merged = os.path.join(final, "merged_output.mp4")

    subprocess.run(
        "ffmpeg -f concat -safe 0 -i list.txt -c copy merged_output.mp4",
        shell=True,
        cwd=tmp,
        capture_output=True,
        text=True,
        check=True,
    )

    shutil.move(os.path.join(tmp, "merged_output.mp4"), merged)
    _clear_tmp(tmp)
    return {
        "job_id": job_id,
        "output_url": f"/media/{job_id}/merged_output.mp4",
        "num_clips": len(paths),
    }


# ─── API Endpoints ─────────────────────────────────────────────────────────────
@app.get("/")
async def root():
    return {"message": "Welcome! POST /render or /batch_render to enqueue rendering."}


@app.post("/render")
async def render(req: RenderRequest):
    job_id = uuid.uuid4().hex
    with status_lock:
        job_statuses[job_id] = {"status": JobState.QUEUED}
    msg = {"type": "render", "payload": req.model_dump(), "job_id": job_id}

    # For local testing, simulate immediate execution instead of pushing to Upstash
    try:
        out = process_render_logic(RenderRequest(**msg["payload"]), job_id)
        with status_lock:
            job_statuses[job_id] = {"status": JobState.DONE, "result": out}
        return {"job_id": job_id, "result": out}
    except Exception as e:
        with status_lock:
            job_statuses[job_id] = {"status": JobState.ERROR, "error": str(e)}
        raise HTTPException(500, detail=f"Simulated job failed: {str(e)}")


@app.post("/batch_render")
async def batch_render(req: BatchRenderRequest):
    job_id = uuid.uuid4().hex
    with status_lock:
        job_statuses[job_id] = {"status": JobState.QUEUED}
    msg = {"type": "batch", "payload": req.model_dump(), "job_id": job_id}

    # For local testing, simulate immediate execution instead of pushing to Upstash
    try:
        out = process_batch_logic(BatchRenderRequest(**msg["payload"]), job_id)
        with status_lock:
            job_statuses[job_id] = {"status": JobState.DONE, "result": out}
        return {"job_id": job_id, "result": out}
    except Exception as e:
        with status_lock:
            job_statuses[job_id] = {"status": JobState.ERROR, "error": str(e)}
        raise HTTPException(500, detail=f"Simulated batch job failed: {str(e)}")


@app.post("/queue/consume")
async def consume(request: Request):
    body = await request.body()
    sig = request.headers.get("Upstash-Signature", "")
    receiver.verify(body=body, signature=sig)  # type: ignore
    msg = QueueMessage.model_validate_json(body)

    with status_lock:
        job_statuses[msg.job_id] = {"status": JobState.PROCESSING}

    try:
        if msg.type == "render":
            out = process_render_logic(RenderRequest(**msg.payload), msg.job_id)
        elif msg.type == "batch":
            out = process_batch_logic(BatchRenderRequest(**msg.payload), msg.job_id)
        else:
            raise ValueError("Invalid message type")

        with status_lock:
            job_statuses[msg.job_id] = {"status": JobState.DONE, "result": out}
        return out

    except Exception as e:
        with status_lock:
            job_statuses[msg.job_id] = {"status": JobState.ERROR, "error": str(e)}
        raise HTTPException(500, detail=f"Job failed: {str(e)}")


@app.get("/status/{job_id}")
async def check_status(job_id: str):
    with status_lock:
        if job_id not in job_statuses:
            raise HTTPException(404, detail="Job ID not found")
        return job_statuses[job_id]
